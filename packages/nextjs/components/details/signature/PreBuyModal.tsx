import { useEffect, useState } from "react";
import { checkIfContract } from "@/contracts/checkIfContract";
import { hasApproved, onClickExecTransaction } from "@/contracts/encodeMultiSendTransaction";
import { getItem } from "@/utils";
import { handleCreateClobAuth } from "@/utils/signature/clobAuth";
import { Button, Divider, Link, Modal, ModalBody, ModalContent, ModalHeader, Spinner, Tooltip } from "@heroui/react";
import { Check } from "lucide-react";
import { useSession } from "next-auth/react";

interface PreBuyModalProps {
  address: string | undefined;
  visible: boolean;
  onClose: () => void;
  onSuccess?: () => void; // 新增成功回调
}

const StatusButton = ({
  state,
  showTooltip,
  onClick,
  successMessage,
  buttonText,
  isDisabled,
}: {
  state: string;
  showTooltip: boolean;
  onClick: () => void;
  successMessage: string;
  buttonText: string;
  isDisabled?: boolean;
}) =>
  state === "success" ? (
    <div className="flex-center gap-1 text-blue-600 text-sm font-semibold">
      <Check strokeWidth={3} className="size-4" />
      {successMessage}
    </div>
  ) : (
    <Tooltip content="Please disconnect and connect with the correct wallet" color="danger" isOpen={showTooltip}>
      <Button
        className="bg-blue-600 text-white"
        isLoading={state === "loading"}
        isDisabled={isDisabled}
        onPress={onClick}
      >
        {buttonText}
      </Button>
    </Tooltip>
  );

const WalletButton = ({ isChecking }: { isChecking: boolean }) => (
  <Link href="/wallet">
    <Button
      className="bg-blue-600 text-white"
      isDisabled={isChecking}
      startContent={isChecking ? <Spinner size="sm" color="white" /> : undefined}
    >
      {isChecking ? "Checking..." : "Wallet"}
    </Button>
  </Link>
);

export default function PreBuyModal({ address, visible, onClose, onSuccess }: PreBuyModalProps) {
  const proxyWallet = getItem(`login_proxyWallet`);
  const [showTooltip, setShowTooltip] = useState(false);
  const [enableState, setEnableState] = useState("none"); // none|loading|success
  const [approveState, setApproveState] = useState("none"); // none|loading|success
  const [isActiveWallet, setIsActiveWallet] = useState<boolean>(false);
  const [isCheckingWallet, setIsCheckingWallet] = useState<boolean>(false); // 新增检查状态
  const { data: session } = useSession();

  useEffect(() => {
    const getProxyAndCheckApprove = async () => {
      if (address && proxyWallet) {
        setIsCheckingWallet(true);

        try {
          const clobApis = getItem("poly_clob_api_key_map") || {};
          const isActiveWallet = !!(proxyWallet && (await checkIfContract(proxyWallet)));
          setIsActiveWallet(isActiveWallet);

          if (isActiveWallet) {
            setApproveState("loading");
            const hasCurrentApi = clobApis && clobApis.hasOwnProperty(address);
            const approved = await hasApproved(proxyWallet);
            setEnableState(hasCurrentApi ? "success" : "none");
            setApproveState(approved);
          }
        } catch (error) {
          console.error("Error checking wallet:", error);
          setIsActiveWallet(false);
        } finally {
          setIsCheckingWallet(false);
        }
      } else {
        setIsCheckingWallet(false);
        setIsActiveWallet(false);
      }
    };

    getProxyAndCheckApprove();
  }, [address, proxyWallet]);

  // 监听两个步骤的完成状态，当都完成时触发成功回调
  useEffect(() => {
    if (enableState === "success" && approveState === "success" && onSuccess) {
      console.log("前置签名两个步骤都已完成，触发成功回调");
      // 延迟一下触发回调，确保状态已稳定
      setTimeout(() => {
        onSuccess();
      }, 500);
    }
  }, [enableState, approveState, onSuccess]);

  return (
    <Modal isOpen={visible} onClose={onClose}>
      <ModalContent>
        <ModalHeader className="flex-center gap-4">
          <div className="py-2">Trade on PredictOne</div>
        </ModalHeader>

        <ModalBody className="px-8 pb-8">
          <div className="flex flex-col gap-2">
            <div className="flex items-center justify-between gap-4">
              <div className="flex flex-col justify-center">
                <div className="font-semibold">Enable Trading</div>
                <div className="text-sm text-gray-600 font-medium">
                  You need to sign this each time you trade on a new browser.
                </div>
              </div>
              {isActiveWallet ? (
                <StatusButton
                  showTooltip={showTooltip}
                  state={enableState}
                  onClick={() => handleCreateClobAuth(setEnableState)}
                  successMessage="Complete"
                  buttonText="Sign"
                />
              ) : (
                <WalletButton isChecking={isCheckingWallet} />
              )}
            </div>

            <Divider className="bg-gray-100" />

            <div className="flex items-center justify-between gap-4">
              <div className="flex flex-col justify-center">
                <div className="font-semibold">Approve Tokens</div>
                <div className="text-sm text-gray-600 font-medium">
                  {isActiveWallet ? "Start trading securely with your USDC." : "Connect your wallet to start trading."}
                </div>
              </div>
              {isActiveWallet ? (
                <StatusButton
                  state={approveState}
                  showTooltip={showTooltip}
                  onClick={() => onClickExecTransaction(address, proxyWallet, session, setApproveState, setShowTooltip)}
                  successMessage="Complete"
                  buttonText="Sign"
                  isDisabled={approveState === "loading" || approveState === "success"}
                />
              ) : (
                <WalletButton isChecking={isCheckingWallet} />
              )}
            </div>
          </div>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
}
