import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@heroui/react";
import { Award, DollarSign, RefreshCcw, TrendingUp } from "lucide-react";
import { useTranslation } from "react-i18next";

interface PointsSystemModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const PointsSystemModal: React.FC<PointsSystemModalProps> = ({ isOpen, onClose }) => {
  const { t } = useTranslation();

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="3xl"
      scrollBehavior="inside"
      classNames={{
        backdrop: "bg-gradient-to-t from-zinc-900 to-zinc-900/10 backdrop-opacity-20",
        base: "border-[#292f46] bg-white dark:bg-[#19172c] text-gray-900 dark:text-[#a8b0d3]",
        header: "border-b-[1px] border-gray-200 dark:border-[#292f46]",
        body: "py-6",
        closeButton: "hover:bg-gray-100 dark:hover:bg-white/5 active:bg-gray-200 dark:active:bg-white/10",
      }}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg">
              <Award className="size-5 text-white" />
            </div>
            <div className="text-xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
              {t("Wallet_Points_System_Title")}
            </div>
          </div>
        </ModalHeader>
        <ModalBody>
          <div className="space-y-4">
            <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
              <div className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
                {t("Wallet_Points_System_Description")}
              </div>
            </div>

            <div className="overflow-x-auto rounded-lg border border-gray-200 dark:border-gray-700">
              <table className="w-full text-sm">
                <thead>
                  <tr className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700">
                    <th className="px-4 py-3 text-left font-semibold text-gray-900 dark:text-gray-100">
                      <div className="flex items-center gap-2">
                        <Award className="size-4 text-yellow-500" />
                        {t("Wallet_Level_Name")}
                      </div>
                    </th>
                    <th className="px-4 py-3 text-left font-semibold text-gray-900 dark:text-gray-100">
                      <div className="flex items-center gap-2">
                        <TrendingUp className="size-4 text-green-500" />
                        {t("Wallet_Monthly_Volume")}
                      </div>
                    </th>
                    <th className="px-4 py-3 text-left font-semibold text-gray-900 dark:text-gray-100">
                      <div className="flex items-center gap-2">
                        <DollarSign className="size-4 text-blue-500" />
                        {t("Wallet_Points_Multiplier")}
                      </div>
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                  {[
                    {
                      level: "Wallet_Level_1",
                      volume: "$0 - 1,000",
                      multiplier: "1.0x",
                      color: "from-gray-400 to-gray-500",
                    },
                    {
                      level: "Wallet_Level_2",
                      volume: "$1,001 - 10,000",
                      multiplier: "1.2x",
                      color: "from-green-400 to-green-500",
                    },
                    {
                      level: "Wallet_Level_3",
                      volume: "$10,001 - 100,000",
                      multiplier: "1.5x",
                      color: "from-blue-400 to-blue-500",
                    },
                    {
                      level: "Wallet_Level_4",
                      volume: "$100,001 - 500,000",
                      multiplier: "2.0x",
                      color: "from-purple-400 to-purple-500",
                    },
                    {
                      level: "Wallet_Level_5",
                      volume: "$500,001 - 1,000,000",
                      multiplier: "2.5x",
                      color: "from-pink-400 to-pink-500",
                    },
                    {
                      level: "Wallet_Level_6",
                      volume: "$1,000,001 - 5,000,000",
                      multiplier: "3.5x",
                      color: "from-orange-400 to-orange-500",
                    },
                    {
                      level: "Wallet_Level_7",
                      volume: "> $5,000,000",
                      multiplier: "5.0x",
                      color: "from-yellow-400 to-yellow-500",
                    },
                  ].map((row, index) => (
                    <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
                      <td className="px-4 py-3">
                        <div className="flex items-center gap-3">
                          <div className={`w-3 h-3 rounded-full bg-gradient-to-r ${row.color}`}></div>
                          <span className="font-medium text-gray-900 dark:text-gray-100">{t(row.level)}</span>
                        </div>
                      </td>
                      <td className="px-4 py-3 text-gray-700 dark:text-gray-300 font-mono">{row.volume}</td>
                      <td className="px-4 py-3">
                        <span
                          className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-bold bg-gradient-to-r ${row.color} text-white`}
                        >
                          {row.multiplier}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg border border-green-200 dark:border-green-800">
                <h4 className="font-semibold text-green-800 dark:text-green-400 mb-2 flex items-center gap-2">
                  <Award className="size-4" />
                  {t("Wallet_Points_Calculation")}
                </h4>
                <div className="text-sm text-green-700 dark:text-green-300">{t("Wallet_Points_Formula")}</div>
              </div>

              <div className="p-4 bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                <h4 className="font-semibold text-blue-800 dark:text-blue-400 mb-2 flex items-center gap-2">
                  <RefreshCcw className="size-4" />
                  {t("Wallet_Settlement_Cycle")}
                </h4>
                <div className="text-sm text-blue-700 dark:text-blue-300">
                  {t("Wallet_Settlement_Description")}
                </div>
              </div>
            </div>
          </div>
        </ModalBody>
        <ModalFooter>
          <Button
            color="primary"
            variant="light"
            onPress={onClose}
            className="font-medium"
          >
            {t("Normal_Close") || "Close"}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default PointsSystemModal;
