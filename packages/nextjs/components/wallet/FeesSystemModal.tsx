import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ead<PERSON> } from "@heroui/react";
import { Award, DollarSign, RefreshCcw, Shield } from "lucide-react";
import { useTranslation } from "react-i18next";

interface FeesSystemModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const FeesSystemModal: React.FC<FeesSystemModalProps> = ({ isOpen, onClose }) => {
  const { t } = useTranslation();

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="md"
      classNames={{
        backdrop: "bg-black/50",
        base: "bg-white dark:bg-gray-800",
        header: "border-b border-gray-200 dark:border-gray-700",
        body: "py-4",
        closeButton: "hover:bg-gray-100 dark:hover:bg-gray-700",
      }}
    >
      <ModalContent>
        <ModalHeader className="flex items-center gap-2 pb-3">
          <Shield className="size-5 text-blue-500" />
          <div className="text-lg font-semibold">{t("Wallet_Fees_System_Title")}</div>
        </ModalHeader>
        <ModalBody>
          <div className="space-y-4">
            <div className="p-3 bg-amber-50 dark:bg-amber-900/20 rounded-lg">
              <p className="text-sm text-amber-800 dark:text-amber-300">{t("Wallet_Fees_System_Description")}</p>
            </div>

            <div className="space-y-3">
              <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <DollarSign className="size-4 text-blue-600" />
                  <h4 className="font-medium text-blue-800 dark:text-blue-400 text-sm">
                    {t("Wallet_Temporary_Collection")}
                  </h4>
                </div>
                <div className="text-xs text-blue-700 dark:text-blue-300">
                  {t("Wallet_Temporary_Collection_Description")}
                </div>
              </div>

              <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <RefreshCcw className="size-4 text-green-600" />
                  <h4 className="font-medium text-green-800 dark:text-green-400 text-sm">{t("Wallet_Full_Refund")}</h4>
                </div>
                <p className="text-xs text-green-700 dark:text-green-300">{t("Wallet_Full_Refund_Description")}</p>
              </div>

              <div className="p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Award className="size-4 text-purple-600" />
                  <h4 className="font-medium text-purple-800 dark:text-purple-400 text-sm">
                    {t("Wallet_Free_For_Real_Users")}
                  </h4>
                </div>
                <p className="text-xs text-purple-700 dark:text-purple-300">
                  {t("Wallet_Free_For_Real_Users_Description")}
                </p>
              </div>
            </div>
          </div>
        </ModalBody>
        <ModalFooter>
          <Button color="primary" variant="light" onPress={onClose} className="font-medium">
            {t("Normal_Close") || "Close"}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default FeesSystemModal;
