import React, { useCallback, useEffect, useMemo, useState } from "react";
import { getClaimHandlingFee } from "@/api/user";
import PreBuyModal from "@/components/details/signature/PreBuyModal";
import ClaimFeesButton from "@/components/wallet/ClaimFeesButton";
import FeesSystemModal from "@/components/wallet/FeesSystemModal";
import PointsSystemModal from "@/components/wallet/PointsSystemModal";
import { useClaimCountdown } from "@/hooks/useClaimCountdown";
import { usePointsAndFees } from "@/hooks/usePointsAndFees";
import { useGlobalState } from "@/services/store/store";
import { getItem } from "@/utils";
import {
  extractClaimedAmount,
  formatUSDCAmount,
  is28DayLimitError,
  processPointsAndFeesData,
} from "@/utils/pointsAndFeesUtils";
import { Button } from "@heroui/react";
import { Award, DollarSign, HelpCircle, RefreshCcw, Shield, TrendingUp } from "lucide-react";
import { toast } from "react-hot-toast";
import { useTranslation } from "react-i18next";

interface PointsAndFeesProps {
  address: string;
  isActiveWallet: boolean;
}

const PointsAndFeesComponent: React.FC<PointsAndFeesProps> = ({ address, isActiveWallet }) => {
  const { t } = useTranslation();
  const { walletType } = useGlobalState();

  // 从 localStorage 获取 API 凭证 - 使用 useMemo 优化性能
  const [refreshKey, setRefreshKey] = useState(0);
  const clobApiMap = useMemo(() => getItem("poly_clob_api_key_map") || {}, []);
  const clobApis = useMemo(() => (address ? clobApiMap[address] : null), [address, clobApiMap]);

  // 使用API Hook获取数据 - 只有在有凭证时才启用
  const { data: pointsFeesData, loading, refetch } = usePointsAndFees(address, isActiveWallet && !!clobApis);

  // Modal状态
  const [isPointsModalOpen, setIsPointsModalOpen] = useState<boolean>(false);
  const [isFeesModalOpen, setIsFeesModalOpen] = useState<boolean>(false);
  const [isShowPreBuyModal, setIsShowPreBuyModal] = useState<boolean>(false);
  const [isClaimingFees, setIsClaimingFees] = useState<boolean>(false);
  const [justCompletedSignature, setJustCompletedSignature] = useState<boolean>(false);

  // 使用工具函数处理数据，提高代码可读性和可维护性
  const calculatedData = useMemo(() => processPointsAndFeesData(pointsFeesData), [pointsFeesData]);
  const { currentPoints, pointsMultiplier, totalFees, feeGained, claimableFees, latestClaimTime } = calculatedData;

  // 使用自定义 Hook 处理28天倒计时逻辑
  const { canClaim, countdown } = useClaimCountdown(latestClaimTime);

  // 领取手续费 - 使用 useCallback 优化性能
  const handleClaimFees = useCallback(async () => {
    if (!address || claimableFees <= 0 || !clobApis || !canClaim) return;

    // 前端再次检查28天限制
    if (!canClaim) {
      toast.error(t("Wallet_Claim_Too_Soon") || `请等待 ${countdown} 后再次领取`);
      return;
    }

    setIsClaimingFees(true);
    try {
      const response = await getClaimHandlingFee(clobApiMap, walletType);

      // 检查响应是否成功 - API返回包含amount_send字段表示成功
      if (response?.data?.amount_send) {
        const amount = extractClaimedAmount(response);
        toast.success(t("Wallet_Claim_Success") || `手续费领取成功！获得 ${formatUSDCAmount(amount)} USDC`);
        // 刷新数据以获取最新的手续费信息
        await refetch();
      } else {
        toast.error(t("Wallet_Claim_Failed") || "手续费领取失败，请重试");
      }
    } catch (error: any) {
      console.error("Claim handling fee error:", error);

      // 使用工具函数检查是否是28天限制错误
      if (is28DayLimitError(error)) {
        toast.error(t("Wallet_Claim_Too_Soon") || `您在28天内已经领取过手续费，请等待 ${countdown} 后再次领取`);
      } else {
        toast.error(t("Wallet_Claim_Error") || "领取过程中发生错误，请重试");
      }
    } finally {
      setIsClaimingFees(false);
    }
  }, [address, claimableFees, clobApis, canClaim, countdown, clobApiMap, walletType, t, refetch]);

  // 刷新所有数据
  const refreshAllData = useCallback(() => {
    refetch();
  }, [refetch]);

  // 处理前置签名成功的回调
  const handlePreBuySuccess = useCallback(() => {
    console.log("前置签名成功，立即刷新状态");

    // 标记刚完成签名
    setJustCompletedSignature(true);

    // 立即更新 refreshKey 触发重新渲染
    setRefreshKey(prev => prev + 1);

    // 关闭 PreBuyModal
    setIsShowPreBuyModal(false);

    // 短暂延迟后刷新数据
    setTimeout(() => {
      console.log("执行数据刷新");
      refetch();

      // 3秒后清除标记
      setTimeout(() => {
        setJustCompletedSignature(false);
      }, 3000);
    }, 100);
  }, [refetch]);

  // 监听 PreBuyModal 关闭，检查是否需要刷新状态
  useEffect(() => {
    if (!isShowPreBuyModal) {
      // PreBuyModal 关闭时，重新检查凭证
      const newClobApiMap = getItem("poly_clob_api_key_map") || {};
      const newClobApis = address ? newClobApiMap[address] : null;

      // 检查是否有新的凭证（前置签名完成）
      if (newClobApis && !clobApis) {
        console.log("前置签名完成，刷新组件状态");

        // 强制重新渲染组件
        setRefreshKey(prev => prev + 1);

        // 延迟刷新数据，确保组件状态已更新
        setTimeout(() => {
          console.log("开始刷新积分和手续费数据");
          refetch();
        }, 200);
      }

      // 即使已有凭证，也检查是否需要刷新数据（可能签名状态有变化）
      else if (newClobApis && clobApis) {
        // 比较凭证是否有变化
        const hasCredentialChanged = JSON.stringify(newClobApis) !== JSON.stringify(clobApis);
        if (hasCredentialChanged) {
          console.log("凭证已更新，刷新数据");
          setRefreshKey(prev => prev + 1);
          setTimeout(() => {
            refetch();
          }, 200);
        }
      }
    }
  }, [isShowPreBuyModal, address, clobApis, refetch]);

  // 监听凭证状态变化，确保前置签名完成后立即刷新
  useEffect(() => {
    if (address && clobApis && isActiveWallet) {
      console.log("检测到凭证可用，刷新积分和手续费数据");
      // 当凭证变为可用时，立即刷新数据
      refetch();
    }
  }, [address, clobApis, isActiveWallet, refetch]);

  if (!isActiveWallet) {
    return null;
  }

  return (
    <div key={refreshKey}>
      {clobApis ? (
        <div className="w-full flex flex-col gap-4">
          {/* 签名成功提示 */}
          {justCompletedSignature && (
            <div className="w-full p-3 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center gap-2 text-green-700">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium">
                  {t("Wallet_Signature_Success") || "签名授权成功！正在刷新数据..."}
                </span>
              </div>
            </div>
          )}

          {/* 积分块 */}
          <div className="w-full flex flex-col p-5 border rounded-lg">
            <div className="flex justify-between items-center">
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <div className="text-xs text-gray-500 font-medium tracking-widest">{t("Wallet_Points_Title")}</div>
                  <HelpCircle
                    className="size-3 text-gray-400 hover:text-gray-600 cursor-pointer"
                    onClick={() => setIsPointsModalOpen(true)}
                  />
                </div>
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    <Award className="size-5 text-yellow-500" />
                    <div className="text-2xl font-semibold">{currentPoints.toLocaleString()}</div>
                  </div>
                  <div className="flex items-center gap-1 px-2 py-1 bg-blue-100 rounded-full">
                    <span className="text-sm font-medium text-blue-700">×{pointsMultiplier}</span>
                  </div>
                </div>
              </div>
              <div
                className="p-2 hover:bg-gray-100 rounded-full cursor-pointer transition-colors"
                onClick={refreshAllData}
              >
                <RefreshCcw className={`size-4 ${loading ? "animate-spin" : ""}`} />
              </div>
            </div>
          </div>

          {/* 手续费块 */}
          <div className="w-full flex flex-col p-5 border rounded-lg">
            <div className="w-full flex items-center gap-2 mb-3">
              <div className="text-xs text-gray-500 font-medium tracking-widest">{t("Wallet_Fees_Title")}</div>
              <HelpCircle
                className="size-3 text-gray-400 hover:text-gray-600 cursor-pointer"
                onClick={() => setIsFeesModalOpen(true)}
              />
            </div>

            {/* 数据展示区域 */}
            <div className="w-full space-y-2 mb-4">
              <div className="w-full flex items-center justify-between p-2 bg-gray-50 rounded-lg border border-gray-100">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-gray-100 rounded-full">
                    <TrendingUp className="size-4 text-gray-600" />
                  </div>
                  <div>
                    <div className="text-xs text-gray-500 font-medium">{t("Wallet_Total_Fees")}</div>
                    <div className="text-lg font-semibold text-gray-800">${totalFees.toFixed(2)}</div>
                  </div>
                </div>
              </div>

              <div className="w-full flex items-center justify-between p-2 bg-blue-50 rounded-lg border border-blue-200">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-100 rounded-full">
                    <DollarSign className="size-4 text-blue-600" />
                  </div>
                  <div>
                    <div className="text-xs text-blue-600 font-medium">{t("Wallet_Fee_Gained") || "手续费收益"}</div>
                    <div className="text-lg font-semibold text-blue-700">${feeGained.toFixed(2)}</div>
                  </div>
                </div>
              </div>

              <div className="w-full flex items-center justify-between p-2 bg-green-50 rounded-lg border border-green-200">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-100 rounded-full">
                    <DollarSign className="size-4 text-green-600" />
                  </div>
                  <div>
                    <div className="text-xs text-green-600 font-medium">{t("Wallet_Claimable_Fees")}</div>
                    <div className="text-xl font-bold text-green-700">${claimableFees.toFixed(2)}</div>
                  </div>
                </div>
                {claimableFees > 0 && (
                  <div className="px-2 py-1 bg-green-100 text-green-700 text-xs font-medium rounded-full">
                    {t("Wallet_Ready_To_Claim")}
                  </div>
                )}
              </div>

              {latestClaimTime && (
                <div className="w-full flex flex-col gap-2">
                  <div className="flex items-center justify-between p-2 bg-yellow-50 rounded-lg border border-yellow-200">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-yellow-100 rounded-full">
                        <Shield className="size-4 text-yellow-600" />
                      </div>
                      <div>
                        <div className="text-xs text-yellow-600 font-medium">
                          {t("Wallet_Latest_Claim_Time") || "最后领取时间"}
                        </div>
                        <div className="text-sm font-medium text-yellow-700">
                          {new Date(latestClaimTime).toLocaleString()}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* 领取按钮 - 使用封装的组件 */}
            <div className="w-full">
              <ClaimFeesButton
                claimableFees={claimableFees}
                isClaimingFees={isClaimingFees}
                canClaim={canClaim}
                countdown={countdown}
                clobApis={clobApis}
                onPress={handleClaimFees}
              />
            </div>
          </div>

          {/* 积分等级体系Modal */}
          <PointsSystemModal isOpen={isPointsModalOpen} onClose={() => setIsPointsModalOpen(false)} />

          {/* 交易手续费Modal */}
          <FeesSystemModal isOpen={isFeesModalOpen} onClose={() => setIsFeesModalOpen(false)} />
        </div>
      ) : (
        <div className="w-full flex flex-col gap-4">
          <div className="w-full flex flex-col p-5 border rounded-lg bg-gray-50">
            <div className="flex justify-between items-center">
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <div className="text-xs text-gray-500 font-medium tracking-widest">{t("Wallet_Points_Title")}</div>
                  <HelpCircle
                    className="size-3 text-gray-400 hover:text-gray-600 cursor-pointer"
                    onClick={() => setIsPointsModalOpen(true)}
                  />
                </div>
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    <Award className="size-5 text-gray-400" />
                    <div className="text-2xl font-semibold text-gray-400">--</div>
                  </div>
                  <div className="flex items-center gap-1 px-2 py-1 bg-gray-200 rounded-full">
                    <span className="text-sm font-medium text-gray-500">×--</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="w-full flex flex-col p-5 border rounded-lg bg-gray-50">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-3">
                  <div className="text-xs text-gray-500 font-medium tracking-widest">{t("Wallet_Fees_Title")}</div>
                  <HelpCircle
                    className="size-3 text-gray-400 hover:text-gray-600 cursor-pointer"
                    onClick={() => setIsFeesModalOpen(true)}
                  />
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-center p-6 bg-yellow-50 rounded-lg border border-yellow-200">
                    <div className="text-center">
                      <Shield className="size-8 text-yellow-600 mx-auto mb-3" />
                      <div className="text-lg font-semibold text-yellow-800 mb-2">
                        {t("Wallet_No_Permission") || "暂无权限"}
                      </div>
                      <div className="text-sm text-yellow-700 mb-4">
                        {t("Wallet_Need_Signature") || "需要签名授权才能查看积分和手续费数据"}
                      </div>
                      <Button
                        size="md"
                        className="bg-yellow-600 text-white hover:bg-yellow-700"
                        onPress={() => setIsShowPreBuyModal(true)}
                      >
                        <div className="flex items-center gap-2">
                          <Shield className="size-4" />
                          {t("Wallet_Sign_Authorization") || "签名授权"}
                        </div>
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 积分等级体系Modal */}
          <PointsSystemModal isOpen={isPointsModalOpen} onClose={() => setIsPointsModalOpen(false)} />

          {/* 交易手续费Modal */}
          <FeesSystemModal isOpen={isFeesModalOpen} onClose={() => setIsFeesModalOpen(false)} />
        </div>
      )}

      <PreBuyModal
        address={address}
        visible={isShowPreBuyModal}
        onClose={() => setIsShowPreBuyModal(false)}
        onSuccess={handlePreBuySuccess}
      />
    </div>
  );
};

export default PointsAndFeesComponent;
