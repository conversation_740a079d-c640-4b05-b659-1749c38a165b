import React, { useEffect, useState } from "react";
import { getClaimHandlingFee } from "@/api/user";
import PreBuyModal from "@/components/details/signature/PreBuyModal";
import FeesSystemModal from "@/components/wallet/FeesSystemModal";
import PointsSystemModal from "@/components/wallet/PointsSystemModal";
import { usePointsAndFees } from "@/hooks/usePointsAndFees";
import { useGlobalState } from "@/services/store/store";
import { getItem } from "@/utils";
import { Button } from "@heroui/react";
import { Award, DollarSign, HelpCircle, RefreshCcw, Shield, TrendingUp } from "lucide-react";
import { toast } from "react-hot-toast";
import { useTranslation } from "react-i18next";

interface PointsAndFeesProps {
  address: string;
  isActiveWallet: boolean;
}

const PointsAndFeesComponent: React.FC<PointsAndFeesProps> = ({ address, isActiveWallet }) => {
  const { t } = useTranslation();
  const { walletType } = useGlobalState();

  // 从 localStorage 获取 API 凭证 - 按照提现的写法
  const [refreshKey, setRefreshKey] = useState(0);
  const clobApis = address ? getItem("poly_clob_api_key_map")?.[address] : null;

  // 使用API Hook获取数据 - 只有在有凭证时才启用
  const { data: pointsFeesData, loading, refetch } = usePointsAndFees(address, isActiveWallet && !!clobApis);

  // Modal状态
  const [isPointsModalOpen, setIsPointsModalOpen] = useState<boolean>(false);
  const [isFeesModalOpen, setIsFeesModalOpen] = useState<boolean>(false);
  const [isShowPreBuyModal, setIsShowPreBuyModal] = useState<boolean>(false);
  const [isClaimingFees, setIsClaimingFees] = useState<boolean>(false);

  // 从API数据中提取值并转换为数字
  const currentPoints = pointsFeesData?.credit ? Math.floor((parseFloat(pointsFeesData.credit) / 1000000) * 100) : 0;
  const pointsMultiplier = pointsFeesData?.credit_ratio ? parseFloat(pointsFeesData.credit_ratio) : 1.0;
  const totalFees = pointsFeesData?.fee ? parseFloat(pointsFeesData.fee) / 1000000 : 0;
  const feeGained = pointsFeesData?.fee_gained ? parseFloat(pointsFeesData.fee_gained) / 1000000 : 0;
  const claimableFees = Math.max(0, feeGained);
  const latestClaimTime = pointsFeesData?.latest_claim_time || null;

  // 领取手续费
  const handleClaimFees = async () => {
    if (!address || claimableFees <= 0 || !clobApis) return;

    setIsClaimingFees(true);
    try {
      const response = await getClaimHandlingFee(clobApis, walletType);

      if (response?.data?.success) {
        toast.success(t("Wallet_Claim_Success") || "手续费领取成功！");
        // 刷新数据以获取最新的手续费信息
        await refetch();
      } else {
        toast.error(t("Wallet_Claim_Failed") || "手续费领取失败，请重试");
      }
    } catch (error) {
      console.error("Claim handling fee error:", error);
      toast.error(t("Wallet_Claim_Error") || "领取过程中发生错误，请重试");
    } finally {
      setIsClaimingFees(false);
    }
  };

  // 刷新所有数据
  const refreshAllData = () => {
    refetch();
  };

  // 监听 PreBuyModal 关闭，检查是否需要刷新
  useEffect(() => {
    if (!isShowPreBuyModal) {
      // PreBuyModal 关闭时，重新检查凭证
      const newClobApis = address ? getItem("poly_clob_api_key_map")?.[address] : null;
      if (newClobApis && !clobApis) {
        // 如果之前没有凭证，现在有了，强制重新渲染
        setRefreshKey(prev => prev + 1);
        // 延迟一下再刷新数据，确保组件已经重新渲染
        setTimeout(() => {
          refetch();
        }, 100);
      }
    }
  }, [isShowPreBuyModal, address, clobApis, refetch]);

  if (!isActiveWallet) {
    return null;
  }

  return (
    <div key={refreshKey}>
      {clobApis ? (
        <div className="w-full flex flex-col gap-4">
          {/* 积分块 */}
          <div className="w-full flex flex-col p-5 border rounded-lg">
            <div className="flex justify-between items-center">
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <div className="text-xs text-gray-500 font-medium tracking-widest">{t("Wallet_Points_Title")}</div>
                  <HelpCircle
                    className="size-3 text-gray-400 hover:text-gray-600 cursor-pointer"
                    onClick={() => setIsPointsModalOpen(true)}
                  />
                </div>
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    <Award className="size-5 text-yellow-500" />
                    <div className="text-2xl font-semibold">{currentPoints.toLocaleString()}</div>
                  </div>
                  <div className="flex items-center gap-1 px-2 py-1 bg-blue-100 rounded-full">
                    <span className="text-sm font-medium text-blue-700">×{pointsMultiplier}</span>
                  </div>
                </div>
              </div>
              <div
                className="p-2 hover:bg-gray-100 rounded-full cursor-pointer transition-colors"
                onClick={refreshAllData}
              >
                <RefreshCcw className={`size-4 ${loading ? "animate-spin" : ""}`} />
              </div>
            </div>
          </div>

          {/* 手续费块 */}
          <div className="w-full flex flex-col p-5 border rounded-lg">
            <div className="w-full flex items-center gap-2 mb-3">
              <div className="text-xs text-gray-500 font-medium tracking-widest">{t("Wallet_Fees_Title")}</div>
              <HelpCircle
                className="size-3 text-gray-400 hover:text-gray-600 cursor-pointer"
                onClick={() => setIsFeesModalOpen(true)}
              />
            </div>

            {/* 数据展示区域 */}
            <div className="w-full space-y-2 mb-4">
              <div className="w-full flex items-center justify-between p-2 bg-gray-50 rounded-lg border border-gray-100">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-gray-100 rounded-full">
                    <TrendingUp className="size-4 text-gray-600" />
                  </div>
                  <div>
                    <div className="text-xs text-gray-500 font-medium">{t("Wallet_Total_Fees")}</div>
                    <div className="text-lg font-semibold text-gray-800">${totalFees.toFixed(2)}</div>
                  </div>
                </div>
              </div>

              <div className="w-full flex items-center justify-between p-2 bg-blue-50 rounded-lg border border-blue-200">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-100 rounded-full">
                    <DollarSign className="size-4 text-blue-600" />
                  </div>
                  <div>
                    <div className="text-xs text-blue-600 font-medium">{t("Wallet_Fee_Gained") || "手续费收益"}</div>
                    <div className="text-lg font-semibold text-blue-700">${feeGained.toFixed(2)}</div>
                  </div>
                </div>
              </div>

              <div className="w-full flex items-center justify-between p-2 bg-green-50 rounded-lg border border-green-200">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-100 rounded-full">
                    <DollarSign className="size-4 text-green-600" />
                  </div>
                  <div>
                    <div className="text-xs text-green-600 font-medium">{t("Wallet_Claimable_Fees")}</div>
                    <div className="text-xl font-bold text-green-700">${claimableFees.toFixed(2)}</div>
                  </div>
                </div>
                {claimableFees > 0 && (
                  <div className="px-2 py-1 bg-green-100 text-green-700 text-xs font-medium rounded-full">
                    {t("Wallet_Ready_To_Claim")}
                  </div>
                )}
              </div>

              {latestClaimTime && (
                <div className="w-full flex items-center justify-between p-2 bg-yellow-50 rounded-lg border border-yellow-200">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-yellow-100 rounded-full">
                      <Shield className="size-4 text-yellow-600" />
                    </div>
                    <div>
                      <div className="text-xs text-yellow-600 font-medium">
                        {t("Wallet_Latest_Claim_Time") || "最后领取时间"}
                      </div>
                      <div className="text-sm font-medium text-yellow-700">
                        {new Date(latestClaimTime).toLocaleString()}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* 领取按钮 - 放在最下面 */}
            <div className="w-full">
              <Button
                size="lg"
                variant="solid"
                onPress={handleClaimFees}
                disabled={claimableFees <= 0 || isClaimingFees || !clobApis}
                className={`w-full font-semibold text-sm transition-all duration-200 ${
                  claimableFees > 0 && !isClaimingFees && clobApis
                    ? "bg-green-600 text-white hover:bg-green-700"
                    : "bg-gray-300 text-gray-500 cursor-not-allowed"
                }`}
              >
                {isClaimingFees ? (
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    {t("Wallet_Claiming_Fees")}
                  </div>
                ) : claimableFees > 0 ? (
                  <div className="flex items-center gap-2">
                    <DollarSign className="size-4" />
                    {t("Wallet_Claim_Fees")} ${claimableFees.toFixed(2)}
                  </div>
                ) : (
                  t("Wallet_No_Fees")
                )}
              </Button>
            </div>
          </div>

          {/* 积分等级体系Modal */}
          <PointsSystemModal isOpen={isPointsModalOpen} onClose={() => setIsPointsModalOpen(false)} />

          {/* 交易手续费Modal */}
          <FeesSystemModal isOpen={isFeesModalOpen} onClose={() => setIsFeesModalOpen(false)} />
        </div>
      ) : (
        <div className="w-full flex flex-col gap-4">
          <div className="w-full flex flex-col p-5 border rounded-lg bg-gray-50">
            <div className="flex justify-between items-center">
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <div className="text-xs text-gray-500 font-medium tracking-widest">{t("Wallet_Points_Title")}</div>
                  <HelpCircle
                    className="size-3 text-gray-400 hover:text-gray-600 cursor-pointer"
                    onClick={() => setIsPointsModalOpen(true)}
                  />
                </div>
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    <Award className="size-5 text-gray-400" />
                    <div className="text-2xl font-semibold text-gray-400">--</div>
                  </div>
                  <div className="flex items-center gap-1 px-2 py-1 bg-gray-200 rounded-full">
                    <span className="text-sm font-medium text-gray-500">×--</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="w-full flex flex-col p-5 border rounded-lg bg-gray-50">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-3">
                  <div className="text-xs text-gray-500 font-medium tracking-widest">{t("Wallet_Fees_Title")}</div>
                  <HelpCircle
                    className="size-3 text-gray-400 hover:text-gray-600 cursor-pointer"
                    onClick={() => setIsFeesModalOpen(true)}
                  />
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-center p-6 bg-yellow-50 rounded-lg border border-yellow-200">
                    <div className="text-center">
                      <Shield className="size-8 text-yellow-600 mx-auto mb-3" />
                      <div className="text-lg font-semibold text-yellow-800 mb-2">
                        {t("Wallet_No_Permission") || "暂无权限"}
                      </div>
                      <div className="text-sm text-yellow-700 mb-4">
                        {t("Wallet_Need_Signature") || "需要签名授权才能查看积分和手续费数据"}
                      </div>
                      <Button
                        size="md"
                        className="bg-yellow-600 text-white hover:bg-yellow-700"
                        onPress={() => setIsShowPreBuyModal(true)}
                      >
                        <div className="flex items-center gap-2">
                          <Shield className="size-4" />
                          {t("Wallet_Sign_Authorization") || "签名授权"}
                        </div>
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 积分等级体系Modal */}
          <PointsSystemModal isOpen={isPointsModalOpen} onClose={() => setIsPointsModalOpen(false)} />

          {/* 交易手续费Modal */}
          <FeesSystemModal isOpen={isFeesModalOpen} onClose={() => setIsFeesModalOpen(false)} />
        </div>
      )}

      <PreBuyModal address={address} visible={isShowPreBuyModal} onClose={() => setIsShowPreBuyModal(false)} />
    </div>
  );
};

export default PointsAndFeesComponent;
